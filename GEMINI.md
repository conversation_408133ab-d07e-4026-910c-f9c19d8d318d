# Agentic Feynman - 智能视频生成工具 (项目上下文说明)

## 项目概述

Agentic Feynman 是一个基于AI Agent的智能视频生成系统。它可以自动从多种材料源（如GitHub项目、PDF论文、网页内容、本地文件或用户聊天指令）提取内容，并生成高质量的讲解视频。

该项目的核心思想是利用一系列AI Agent来自动化视频制作流程，包括：
1.  **材料解析**: 从源内容中提取关键信息。
2.  **素材生成**: 根据用户目的和内容特点，生成适合视频讲解的素材。
3.  **分镜设计**: 创建动画脚本（Storyboard），规划视频的视觉呈现和叙述逻辑。
4.  **视频渲染**: 使用Manim等工具将故事板渲染成最终视频。

系统具有智能意图分类、内容体裁识别、素材智能扩充等功能，能够根据不同内容类型（如学术论文、GitHub项目、教程等）和用户目标生成定制化的视频。

## 技术栈

- **主要语言**: Python 3.11+
- **核心框架**: CAMEL-AI, DSPy, Manim
- **AI模型**: 主要使用Google Gemini系列模型 (如 `gemini-2.5-flash`, `gemini-2.5-pro`)，也集成了OpenAI和Anthropic Claude模型的兼容接口。
- **依赖管理**: `uv` (通过 `pyproject.toml`)
- **其他关键库**: Selenium, OpenCV, ChromaDB (RAG), NLTK, scikit-learn, PyYAML, loguru, BeautifulSoup4等。

## 项目结构

项目包含多个核心目录和文件：

- `agents/`: 存放各种AI Agent的实现，如意图分类、大纲生成、素材生成、DSL生成等。
- `config/`: 配置文件目录，核心配置文件为 `config.yaml`。
- `data/`: 存放数据文件，如RAG数据库。
- `docs/`, `design/`: 文档和设计相关文件。
- `examples/`, `output/`: 示例和输出文件目录。
- `feynman_workflow/`, `process_storyboard/`: 核心工作流和分镜处理模块。
- `prompts/`: 存放各种Prompt模板，按体裁分类。
- `tools/`: 存放各种工具，特别是素材扩充工具。
- `utils/`: 通用工具函数。
- `tests/`: 测试文件。

核心脚本包括 `feynman_workflow_refactored.py`，这是启动整个视频生成流程的主入口。

## 配置 (`config/config.yaml`)

配置文件是驱动项目行为的核心。它定义了：
- 使用的AI模型及其API密钥。
- 输入材料源（GitHub URL, PDF URL, 聊天目的等）。
- 各个Agent和工具的开关。
- 视频渲染参数（分辨率、背景色等）。
- RAG和工作流控制选项。

## 构建和运行

### 环境准备

1. 确保安装了 Python 3.11 或更高版本。
2. 安装依赖: `pip install -r requirements.txt` 或推荐使用 `uv pip install -r pyproject.toml`。

### 运行流程

1.  **配置输入**: 编辑 `config/config.yaml` 文件，设置 `material.intent_input` 部分，指定输入源（URL、文件路径或聊天目的）。
2.  **执行主脚本**:
    ```bash
    python feynman_workflow_refactored.py
    ```
    该脚本会根据配置自动执行意图分类、材料处理、分镜设计和视频渲染等步骤。
3.  **查看输出**: 生成的视频文件通常位于 `output/{topic}/videos/` 目录下。

### 关键脚本

- `feynman_workflow_refactored.py`: 主工作流脚本。
- `agents/` 目录下的各种 `.py` 文件: 实现具体功能的Agent。
- `tools/enhance_tools/` 目录下的各种工具: 提供素材扩充功能。
- `process_storyboard_refactored.py`: 处理分镜并生成Manim代码。

## 开发约定与扩展

### 内容体裁 (Genre)

系统支持多种内容体裁（如 `paper`, `github`, `tutorial`, `blog` 等），每种体裁有对应的Prompt模板（位于 `prompts/storyboard_prompts/`），用于指导分镜设计。添加新体裁需要修改 `agents/generate_manim_dsl_agent_refactored.py` 中的 `ContentGenre` 枚举和 `GenreDetector`，并在 `prompts/storyboard_prompts/` 下创建对应的Prompt文件。

### 素材扩充工具 (Enhancement Tools)

系统采用模块化的工具架构来扩充素材。工具分为四大类：内容结构化组织、多模态呈现、深度洞察、智能交互。添加新工具需要：
1.  在 `tools/enhance_tools/` 下创建新工具类，继承 `EnhancementTool` 基类。
2.  实现 `get_tool_info`, `can_apply`, `apply_tool`, `generate_intro` 等方法。
3.  在 `agents/material_enhancement.py` 的 `ToolRegistry` 中注册新工具。
4.  在 `config.yaml` 中添加相应的开关配置。

### 分镜与动画

分镜设计由 `agents/generate_manim_dsl_agent_refactored.py` 负责，它会根据内容体裁和用户目的生成JSON格式的故事板。然后 `process_storyboard_refactored.py` 会将故事板转换为Manim Python代码并执行渲染。

## 其他说明

- 项目使用 `loguru` 进行日志记录，方便调试。
- 项目集成了RAG（Retrieval-Augmented Generation）能力，用于增强内容理解和生成。
- 配置文件中包含背景音乐、转场效果等视频后处理选项。