"""
FeynmanScene class for Manim animations with region management.
"""

import json
import os
import pickle
import shutil
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Callable, Optional

import cv2
from loguru import logger
from manim import *
from manim_voiceover import VoiceoverScene, helper

from dsl.v2.core.transition_effects import TransitionManager
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.common import Config
from utils.edgetts_service import EdgeTTSService
from utils.feynman_scene import add_wrapped_subcaption, append_to_json_file
from utils.transition_helper import load_scene_state, save_scene_state

VoiceoverScene.add_wrapped_subcaption = add_wrapped_subcaption
helper.append_to_json_file = append_to_json_file

config_obj = Config()
manim_config = config_obj.config.get("manim", {})

# 设置像素分辨率
# config.pixel_width = manim_config.get("width", 1920)
# config.pixel_height = manim_config.get("height", 1080)


class FeynmanScene(VoiceoverScene):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = logger  # 使 logger 在场景实例中可用
        self.set_speech_service(EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"), create_subcaption=True)
        self.current_mobj: Mobject | None = None
        self.full_screen_rect = Rectangle(height=config.frame_height, width=config.frame_width)

        # 设置frame尺寸：保持frame_width不变，根据像素比例调整frame_height
        pixel_aspect_ratio = config.pixel_width / config.pixel_height

        # 保持默认的frame_width不变
        # config.frame_width 保持默认值 (通常是14.222)

        # 根据像素比例调整frame_height
        # 如果像素是竖屏(9:16)，那么frame也应该是竖屏比例
        config.frame_height = config.frame_width / pixel_aspect_ratio

        self.frame_width = config.frame_width
        self.frame_height = config.frame_height

        transition_config = config_obj.config.get("transition", {})

        # 背景相关配置
        background_config = config_obj.config.get("background", {})
        self.background_type = background_config.get("type", "hyperbolic_network")  # 默认使用双曲网络背景
        self.background_config = background_config  # 保存完整的背景配置

        # 转场相关属性
        self.transition_enabled = transition_config.get("enable", False)  # 是否启用转场效果
        self.default_transition_type = transition_config.get("type", None)  # 默认转场类型，None表示随机选择
        self.transition_run_time = transition_config.get("run_time", 1.0)  # 转场动画时长

        # 场景状态序列化相关
        self.scene_states_dir = Path(transition_config.get("state_dir", "output/scene_states"))
        self.scene_states_dir.mkdir(parents=True, exist_ok=True)
        self.current_scene_id = None

        # 性能优化
        self.static_segments = []
        self.temp_dir = Path(tempfile.mkdtemp(prefix="temp_static"))
        logger.info(f"临时文件夹: {self.temp_dir}")

        self.temp_dir.mkdir(exist_ok=True)
        self.segment_counter = 0
        self.timeline = []

    def clear_current_mobj(
        self,
        transition_type: Optional[str] = None,
        run_time: Optional[float] = None,
        new_mobj: Optional[Mobject] = None,
    ):
        """
        清除当前场景中的对象，使用精美的转场效果

        Args:
            transition_type: 转场效果类型，None表示随机选择
            run_time: 转场动画时长，None使用默认值
        """
        if self.current_mobj:
            if not self.transition_enabled:
                # 如果禁用转场，使用原来的简单淡出
                logger.warning("Transition is disabled, using simple fade out")
                self.play(FadeOut(self.current_mobj))
            else:
                # 使用转场效果
                actual_transition_type = transition_type or self.default_transition_type
                logger.info(
                    f"actual_transition_type: {actual_transition_type}, default: {self.default_transition_type}, input: {transition_type}"
                )
                actual_run_time = run_time or self.transition_run_time

                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=self.current_mobj,
                    new_mobj=new_mobj,
                    transition_type=actual_transition_type,
                    run_time=actual_run_time,
                )

            self.remove(self.current_mobj)
            self.current_mobj = None

    def save_scene_state(self, content_type: str, mobject_id: str):
        if not hasattr(self, "first_mobj"):
            scene_name = f"{self.__class__.__name__}_first"
            self.first_mobj = self.current_mobj
        else:
            scene_name = f"{self.__class__.__name__}_last"
        save_scene_state(
            scene_name=scene_name,
            current_mobj=self.current_mobj,
            save_dir=str(self.scene_states_dir),
            content_type=content_type,
            mobject_id=mobject_id,
        )

    def load_scene_state(self, scene_id: str, is_old_mobject: bool = True) -> Optional[Mobject]:
        if is_old_mobject:
            scene_name = f"{scene_id}_last"
            if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                # 可能这个分镜只有一个action，所以没有_last，只有_first
                scene_name = f"{scene_id}_first"
                if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                    # 兼容直接以mobject_id为名字存储的文件
                    scene_name = scene_id
        else:
            scene_name = f"{scene_id}_first"
            if not (self.scene_states_dir / (scene_name + ".pkl")).exists():
                # 兼容直接以mobject_id为名字存储的文件
                scene_name = scene_id
        return load_scene_state(save_dir=str(self.scene_states_dir), scene_id=scene_name, is_old_mobject=is_old_mobject)

    def create_gray_background(self):
        """创建简单的纯灰色背景"""
        # 创建纯色背景
        background = Rectangle(
            width=self.frame_width,
            height=self.frame_height,
            fill_opacity=1,
            stroke_width=0,
            fill_color=BLACK,  # 调暗背景色，从GRAY_D改为GRAY_E
        )

        return background

    def create_hyperbolic_network_background(self):
        # 获取主题颜色
        primary_color = ThemeUtils.get_color("primary")
        background_color = ThemeUtils.get_color("background")

        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=self.frame_width, height=self.frame_height, fill_opacity=1, stroke_width=0
        ).set_color_by_gradient([primary_color, background_color, primary_color])

        # Create hyperbolic network with regular pattern
        network = VGroup()

        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10

        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=primary_color,
            )
            network.add(line)

        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(radius=radius, stroke_width=1.2, stroke_opacity=0.3, stroke_color=primary_color)
            network.add(circle)

        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines

                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles

                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])

                # Create a curved path between points
                control = (
                    np.array([(start[0] + end[0]) * 0.5, (start[1] + end[1]) * 0.5, 0]) * 0.5
                )  # Pull control point toward center for hyperbolic effect

                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=primary_color,
                )
                network.add(curve)

        # Scale the network to fit the screen
        network.scale(0.9)

        # Add a central node
        central_node = Circle(
            radius=0.15, fill_opacity=0.5, stroke_width=1.5, stroke_color=primary_color, fill_color=primary_color
        )

        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])

                node = Circle(
                    radius=0.08, fill_opacity=0.4, stroke_width=1, stroke_color=primary_color, fill_color=primary_color
                ).move_to(position)
                nodes.add(node)

        network.add(central_node, nodes)

        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=background_color,
        )

        return gradient, network, center_mask

    def create_scrolling_text(self):
        """创建滚动文字组"""
        text_group = VGroup()  # 恢复使用VGroup，因为只有文字对象

        # 调整字体大小和间距，让屏幕显示更多文字
        text_spacing_x = 8  # 水平间距从12缩小到8
        text_spacing_y = 6  # 垂直间距从9缩小到6

        # 计算需要的行数和列数（确保覆盖整个屏幕加上更大的缓冲区）
        rows = int(self.frame_height / text_spacing_y) + 8  # 增加更多缓冲行
        cols = int(self.frame_width / text_spacing_x) + 8  # 增加更多缓冲列

        text = pickle.load(open("assets/bg_text.pickle", "rb"))

        # 创建多行多列的文字
        for row in range(rows):
            for col in range(cols):
                text_copy = text.copy()

                # 计算位置（从左下角开始，向右上角倾斜排列，调整初始位置确保文字完整显示）
                # 考虑到文字大小和旋转角度，调整起始位置
                start_x = -self.frame_width / 2 - text_spacing_x * 1.5  # 减少左边距，让文字更早进入屏幕
                start_y = -self.frame_height / 2 - text_spacing_y * 1.5  # 减少下边距，让文字更早进入屏幕

                x_pos = start_x + col * text_spacing_x
                y_pos = start_y + row * text_spacing_y

                text_copy.move_to(np.array([x_pos, y_pos, 0]))

                # 添加旋转角度，使文字倾斜15度（从左下到右上）
                text_copy.rotate(PI / 12)  # 旋转15度 (PI/12 = 15度)

                text_group.add(text_copy)

        # 保存间距信息，供滚动函数使用
        text_group.text_spacing_x = text_spacing_x
        text_group.text_spacing_y = text_spacing_y

        return text_group

    def _add_gray_background(self):
        """添加纯灰色背景环境"""
        background = self.create_gray_background()
        self.add(background)

        # 添加滚动文字效果，确保在最上层
        scrolling_text = self.create_scrolling_text()
        self.add(scrolling_text)

        # 添加滚动动画（从左下向右上滚动）
        def scroll_text(mob, dt):
            # 向右上角滚动
            mob.shift(dt * np.array([0.3, 0.2, 0]))  # 减慢速度，使滚动更平滑

            # 平滑的循环重置逻辑
            center = mob.get_center()

            # 使用动态计算的间距
            spacing_x = mob.text_spacing_x
            spacing_y = mob.text_spacing_y

            # 当文字移出屏幕右边界时，平滑重置到左边
            if center[0] > self.frame_width / 2 + spacing_x:
                mob.shift(np.array([-(self.frame_width + spacing_x * 2), 0, 0]))

            # 当文字移出屏幕上边界时，平滑重置到下边
            if center[1] > self.frame_height / 2 + spacing_y:
                mob.shift(np.array([0, -(self.frame_height + spacing_y * 2), 0]))

        scrolling_text.add_updater(scroll_text)

    def _add_hyperbolic_network_background(self):
        """添加双曲网络背景环境"""
        gradient, network, center_mask = self.create_hyperbolic_network_background()

        # Add the background to the scene
        self.add(gradient, center_mask)

        # Add the network and start rotation animation in the background
        self.add(network)

        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # 固定旋转速度

        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.add(network)

    def add_background(self):
        """
        根据配置添加不同类型的背景
        支持的背景类型：
        - 'gray': 纯灰色背景
        - 'hyperbolic_network': 双曲网络背景（默认）
        """
        self.logger.info(f"Adding background type: {self.background_type}")

        if self.background_type == "gray":
            try:
                self._add_gray_background()
            except Exception as e:
                self.logger.error(f"Failed to add gray background: {e}")
                import traceback

                logger.error(traceback.format_exc())
                pass

        elif self.background_type == "hyperbolic_network":
            self._add_hyperbolic_network_background()

        else:
            # 默认背景类型或未知类型时，使用双曲网络背景
            self.logger.warning(f"Unknown background type: {self.background_type}, using hyperbolic_network as default")
            self._add_hyperbolic_network_background()

    def play(self, *args, **kwargs):
        """记录动态片段"""
        self.timeline.append({"type": "dynamic"})
        super().play(*args, **kwargs)
        self.renderer.update_frame(self)  # HACK，让最后一帧的渲染状态生效

    def wait(
        self,
        duration: float = DEFAULT_WAIT_TIME,
        stop_condition: Callable[[], bool] | None = None,
        frozen_frame: bool | None = None,
    ):
        # 性能优化版，直接复制帧，跳过逐帧渲染
        logger.info(f"记录静态片段: {duration}秒")

        # 渲染当前帧并保存
        frame_path = self.temp_dir / f"static_frame_{self.segment_counter}.png"
        # self.renderer.update_frame(self)
        frame = self.renderer.get_frame()

        # 保存帧
        frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(frame_path), frame_bgr)

        # 记录静态片段信息
        segment_info = {
            "type": "static",
            "frame_path": str(frame_path),
            "duration": duration,
            "start_time": self.renderer.time,
            "segment_id": self.segment_counter,
        }
        self.static_segments.append(segment_info)
        self.timeline.append(segment_info)

        # 跳过时间但不渲染
        self.renderer.time += duration
        self.segment_counter += 1

    def render(self, **kwargs):
        """重写渲染方法"""
        render_cost = static_cost = concat_cost = 0.0

        # 第一步：正常渲染（但跳过静态片段）
        # construct() 在 super().render() 内部被调用,
        # 会填充 self.timeline 和 self.static_segments,
        # 并生成动态片段的视频文件。
        render_start = time.time()
        super().render(**kwargs)
        render_cost = time.time() - render_start

        # 第二步：处理静态片段
        if self.static_segments:
            static_start = time.time()
            self.process_static_segments()
            static_cost = time.time() - static_start

            # 第三步：拼接最终视频
            concat_start = time.time()
            self.create_final_video()
            concat_cost = time.time() - concat_start
        else:
            # 如果没有静态片段, Manim已经生成了正确的视频
            pass

        res = {
            "action": getattr(self, "scene_method_name", "unknown"),
            "render_cost": round(render_cost, 4),
            "static_cost": round(static_cost, 4),
            "concat_cost": round(concat_cost, 4),
            "total_cost": round(render_cost + static_cost + concat_cost, 4),
        }
        logger.info(json.dumps(res, ensure_ascii=False))

    def process_static_segments(self):
        """处理所有静态片段"""
        for segment in self.static_segments:
            self.create_static_video(segment)

    def create_static_video(self, segment):
        """从单帧创建静态视频"""
        start = time.time()
        frame_path = segment["frame_path"]
        duration = segment["duration"]
        segment_id = segment["segment_id"]

        output_path = self.temp_dir / f"static_video_{segment_id}.mp4"

        # 获取场景配置
        fps = config.frame_rate
        width = config.pixel_width
        height = config.pixel_height

        cmd = [
            "ffmpeg",
            "-y",
            "-loop",
            "1",
            "-i",
            str(frame_path),
            "-t",
            str(duration),
            "-r",
            str(fps),
            "-s",
            f"{width}x{height}",
            "-pix_fmt",
            "yuv420p",
            "-c:v",
            "libx264",
            "-preset",
            "ultrafast",
            str(output_path),
        ]

        try:
            _ = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(
                f"  静态视频片段 {segment_id} 创建成功 {output_path}, ({duration}s), {time.time() - start:.4f}s"
            )
            segment["video_path"] = str(output_path)
        except subprocess.CalledProcessError as e:
            logger.error(f"创建静态视频失败: {e}")
            logger.error(f"错误输出: {e.stderr}")

    def create_final_video(self):
        """根据时间线拼接最终视频"""
        final_output_path = self.renderer.file_writer.movie_file_path
        if not final_output_path:
            logger.error("错误：无法确定最终输出文件路径。")
            return

        dynamic_paths_iter = iter(self.renderer.file_writer.partial_movie_files)
        static_videos_by_id = {s["segment_id"]: s.get("video_path") for s in self.static_segments}

        concat_list_path = self.temp_dir / "concat_list.txt"

        with open(concat_list_path, "w") as f:
            for segment in self.timeline:
                path = None
                if segment["type"] == "dynamic":
                    try:
                        path = next(dynamic_paths_iter)
                    except StopIteration:
                        logger.warning("警告：时间线上的动态片段多于Manim生成的视频文件。")
                        continue
                elif segment["type"] == "static":
                    segment_id = segment["segment_id"]
                    path = static_videos_by_id.get(segment_id)
                    if not path:
                        logger.warning(f"警告：未找到静态片段 {segment_id} 的视频文件，跳过此片段。")
                        continue

                if path:
                    # 在拼接列表中使用绝对路径以增加稳健性
                    abs_path = os.path.abspath(path)
                    f.write(f"file '{abs_path}'\n")

        if not concat_list_path.exists() or concat_list_path.stat().st_size == 0:
            logger.info("拼接列表为空，不生成最终视频。")
            return

        temp_final_output = Path(final_output_path).parent / f"temp_optimized_{Path(final_output_path).name}"

        cmd = [
            "ffmpeg",
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            str(concat_list_path),
            "-c",
            "copy",
            str(temp_final_output),
        ]

        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"最终视频创建成功: {temp_final_output}")
            # 将拼接好的视频移动到Manim期望的最终位置
            temp_final_output_with_audio = (
                Path(final_output_path).parent / f"temp_optimized_with_audio_{Path(final_output_path).name}"
            )
            command = [
                "ffmpeg",
                "-i",
                temp_final_output,
                "-i",
                Path(final_output_path).with_suffix(".wav"),
                "-c:v",
                "copy",  # 复制视频流
                "-c:a",
                "aac",  # 将音频编码为 aac
                "-strict",
                "-2",  # aac 是实验性编码器，需要此参数
                temp_final_output_with_audio,
            ]
            subprocess.run(command, capture_output=True, text=True, check=True)
            logger.info(f"最终拼接音频成功: {temp_final_output_with_audio}")
            shutil.move(str(temp_final_output_with_audio), final_output_path)
            logger.info(f"已将优化视频移动到: {final_output_path}")
        except subprocess.CalledProcessError as e:
            logger.error(f"视频拼接失败: {e}")
            logger.error(f"FFmpeg stderr: {e.stderr}")

        """清理临时文件"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            logger.info("临时文件已清理")
