from manim import *


class GroupedBarChart(VGroup):
    def __init__(self, data, series_colors, **kwargs):
        super().__init__(**kwargs)
        self.data = data
        self.series_colors = series_colors
        self.dataset_names = list(data.keys())
        self.series_names = list(series_colors.keys())

        # 计算数据范围
        all_values = [value for dataset in data.values() for value in dataset.values()]
        max_value = max(all_values)
        min_value = min(all_values)
        self.y_min_padded = min(0, min_value)  # min(0, min_value - (max_value - min_value) * 0.1)
        self.y_max_padded = max(0, max_value + (max_value - min_value) * 0.1)
        y_step = 20 if (max_value - min_value) > 100 else 10

        # 创建坐标轴
        num_datasets = len(self.dataset_names)
        x_range = [0, num_datasets + 1, 1]
        y_range = [self.y_min_padded, self.y_max_padded, y_step]

        self.axes = Axes(
            x_range=x_range,
            y_range=y_range,
            x_length=10,
            y_length=6,
            axis_config={
                "color": GREY_A,
                "stroke_width": 3,
                "include_numbers": True,
                "font_size": 28,
            },
            x_axis_config={
                "include_numbers": False,
                "tick_size": 0.1,
            },
            y_axis_config={
                "decimal_number_config": {"num_decimal_places": 0},
            },
        ).add_coordinates()

        self.axes.center()

        # 初始化条形（高度为0）
        self.bars = VGroup()
        self.bar_labels = VGroup()
        self._create_bars(initial_height=0)

        # 添加组件
        self.add(self.axes, self.bars)

        # 添加轴标签
        self.x_axis_label = self.axes.get_x_axis_label(Text("数据集").scale(0.7))
        self.y_axis_label = self.axes.get_y_axis_label(
            Text("性能指标").scale(0.7).rotate(90 * DEGREES),
            edge=LEFT,
            direction=LEFT,
            buff=0.4,
        )
        self.add(self.x_axis_label, self.y_axis_label)

    def _create_bars(self, initial_height=None):
        """创建条形"""
        self.bars = VGroup()  # .clear()
        self.bar_labels = VGroup()  # .clear()

        num_series = len(self.series_names)
        group_width = 0.8
        bar_width = group_width / num_series
        bar_spacing = 0.05

        for i, dataset_name in enumerate(self.dataset_names):
            x_position = i + 1
            group_center_point = self.axes.coords_to_point(x_position, 0)

            for j, series_name in enumerate(self.series_names):
                if initial_height is not None:
                    value = initial_height
                else:
                    value = self.data[dataset_name][series_name]

                offset_from_center = (j - (num_series - 1) / 2) * (bar_width + bar_spacing)

                bar = Rectangle(
                    width=self.axes.x_axis.get_unit_size() * bar_width,
                    height=(self.axes.y_axis.get_unit_size() * abs(value) if value != 0 else 0.01),
                    fill_color=self.series_colors[series_name],
                    fill_opacity=0.7,
                    stroke_color=BLACK,
                    stroke_width=0,
                )

                offset_in_screen_units = self.axes.x_axis.get_unit_size() * offset_from_center
                bar.move_to(group_center_point + offset_in_screen_units * RIGHT)
                if value >= 0:
                    bar.align_to(self.axes.coords_to_point(x_position, 0), DOWN)
                else:
                    bar.align_to(self.axes.coords_to_point(x_position, 0), UP)

                # 存储条形的元数据
                bar.dataset_index = i
                bar.series_index = j
                bar.series_name = series_name
                bar.dataset_name = dataset_name

                self.bars.add(bar)

    def change_bar_values(self, new_data=None):
        """模拟 BarChart 的 change_bar_values 方法"""
        if new_data is None:
            new_data = self.data

        new_bars = VGroup()

        for bar in self.bars:
            dataset_name = bar.dataset_name
            series_name = bar.series_name
            new_value = new_data[dataset_name][series_name]

            # 创建新的条形
            new_bar = Rectangle(
                width=bar.width,
                height=(self.axes.y_axis.get_unit_size() * abs(new_value) if new_value != 0 else 0.01),
                fill_color=bar.fill_color,
                fill_opacity=bar.fill_opacity,
                stroke_color=bar.stroke_color,
                stroke_width=bar.stroke_width,
            )

            # 保持相同的x位置，调整y位置
            new_bar.move_to([bar.get_center()[0], bar.get_center()[1], 0])

            if new_value >= 0:
                x_position = bar.dataset_index + 1
                new_bar.align_to(self.axes.coords_to_point(x_position, 0), DOWN)
            else:
                x_position = bar.dataset_index + 1
                new_bar.align_to(self.axes.coords_to_point(x_position, 0), UP)

            # 保持元数据
            new_bar.dataset_index = bar.dataset_index
            new_bar.series_index = bar.series_index
            new_bar.series_name = bar.series_name
            new_bar.dataset_name = bar.dataset_name

            new_bars.add(new_bar)

        # 返回变换动画
        return Transform(self.bars, new_bars)

    def get_bar_labels(self, font_size=20):
        """获取条形标签"""
        labels = VGroup()
        for bar in self.bars:
            dataset_name = bar.dataset_name
            series_name = bar.series_name
            value = self.data[dataset_name][series_name]

            label = Text(f"{value}", font_size=font_size, color=WHITE)
            if value >= 0:
                label.next_to(bar, UP, buff=0.1)
            else:
                label.next_to(bar, DOWN, buff=0.1)
            labels.add(label)

        return labels

    def get_dataset_labels(self, font_size=24):
        """获取数据集标签"""
        labels = VGroup()
        for i, dataset_name in enumerate(self.dataset_names):
            x_position = i + 1
            label_point = self.axes.coords_to_point(x_position, 0)

            label = Text(dataset_name, font_size=font_size, color=WHITE).rotate(45 * DEGREES)
            label.next_to(label_point, DOWN, buff=0.1)
            labels.add(label)

        return labels


class GroupedBarChartScene(Scene):
    def construct(self):
        # 数据定义
        data = {
            "数据集 A": {"算法1": 85, "算法2": -35, "算法3": 88},
            "数据集 B": {"算法1": 92, "算法2": 88, "算法3": 88},
            "数据集 C": {"算法1": 70, "算法2": 65, "算法3": 68},
            "数据集 D": {"算法1": 95, "算法2": 80, "算法3": 97},
            "数据集 E": {"算法1": 75, "算法2": 72, "算法3": 77},
        }

        series_colors = {
            "算法1": BLUE,
            "算法2": RED,
            "算法3": GREEN,
        }

        # 创建分组条形图
        chart = GroupedBarChart(data, series_colors)
        self.add(chart)

        # 使用 change_bar_values 动画
        self.play(chart.change_bar_values(), run_time=3)

        # 添加条形标签
        bar_labels = chart.get_bar_labels(font_size=20)
        self.play(Write(bar_labels))

        # 添加数据集标签
        dataset_labels = chart.get_dataset_labels(font_size=24)
        self.play(Write(dataset_labels))

        # 创建图例
        legend_items = VGroup()
        for series_name, color in series_colors.items():
            color_box = Square(side_length=0.3, fill_color=color, fill_opacity=1.0, stroke_width=0)
            series_text = Text(series_name, font_size=24, color=WHITE)

            legend_item = VGroup(color_box, series_text)
            legend_item.arrange(RIGHT, buff=0.2)
            legend_items.add(legend_item)

        legend_items.arrange(DOWN, buff=0.4)
        legend_items.to_corner(UR, buff=0.5)

        self.play(FadeIn(legend_items))

        self.wait(2)
