"""
effect: |
  创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。

use_cases:
  - 可视化数据趋势和比较（折线图、条形图）
  - 展示多个类别在不同指标上的表现（雷达图）
  - 在视频演示中动态呈现统计数据
  - 对比不同数据集之间的关系

params:
  scene:
    type: FeynmanScene
    desc: Man<PERSON>场景实例（由系统自动传入）
  chart_type:
    type: str
    desc: 图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图）
    required: true
  data:
    type: dict | list[dict]
    desc: 图表数据。单数据集为dict格式（如{"A":10,"B":20}），多数据集为dict列表
    required: true
  narration:
    type: str
    desc: 在图表显示时播放的语音旁白文本
    required: true
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  animation_style:
    type: str
    desc: 图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update', 'dynamic', bar图优先用dynamic
    default: fadeIn
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  dataset_names:
    type: list[str]
    desc: 多数据集时，用于图例的数据集名称列表
    default: None
  x_label:
    type: str
    desc: x轴标签（条形图和折线图有效）
    default: None
  y_label:
    type: str
    desc: y轴标签（条形图和折线图有效）
    default: None

dsl_examples:
  - type: animate_chart
    params:
      chart_type: bar
      data:
        苹果: 75
        香蕉: 120
        橙子: 90
      title: 水果销量
      narration: 这是本月水果销量的条形图。
      x_label: 水果
      y_label: 销量 (千克)
      dataset_names: ["实际销量"]
      animation_style: dynamic
  - type: animate_chart
    params:
      chart_type: line
      data:
        - 第一季度: 50
          第二季度: 65
          第三季度: 80
          第四季度: 70
        - 第一季度: 40
          第二季度: 50
          第三季度: 60
          第四季度: 90
      title: 产品A vs 产品B 季度销售额
      dataset_names: ["产品A", "产品B"]
      narration: 此折线图比较了产品A和产品B的季度销售额。
      x_label: 季度
      y_label: 销售额 (万元)
  - type: animate_chart
    params:
      chart_type: radar
      data:
        - 性能: 8
          价格: 6
          外观: 9
          易用性: 7
          可靠性: 8
        - 性能: 9
          价格: 4
          外观: 7
          易用性: 8
          可靠性: 9
      title: 产品对比
      dataset_names: ["产品X", "产品Y"]
      narration: 这个雷达图展示了两款产品在五个维度上的评分对比。

notes:
  - 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点
  - 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点
  - 对于条形图，动画效果是固定的动态增长效果，animation_style参数会被忽略。
"""

from typing import TYPE_CHECKING, Optional

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.grouped_bar_chart import GroupedBarChart
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.title_utils import create_title

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


def _get_colors(num_datasets: int) -> list[str]:
    """Generate a list of different colors for multiple datasets.

    Args:
        num_datasets: Number of datasets to generate colors for

    Returns:
        List of color constants
    """
    # 使用主题系统的图表颜色
    return ThemeUtils.get_cyclic_colors(num_datasets)


def _normalize_data(data: dict | list[dict]) -> tuple[list[dict], list[str]]:
    """Normalize data into a list format and return dataset names.

    Args:
        data: Data in dict or list of dicts format

    Returns:
        Tuple of (normalized data list, dataset names list)
    """
    if isinstance(data, dict):
        # If it's a single dict, wrap it in a list
        normalized_data = [data]
        dataset_names = ["数据集1"]  # Default dataset name
    else:
        normalized_data = data
        # Use indices as default dataset names
        dataset_names = [f"数据集{i+1}" for i in range(len(data))]

    return normalized_data, dataset_names


def _create_enhanced_bar_chart(
    normalized_data: list[dict],
    dataset_names: list[str],
    colors: list[str],
    x_label: str = None,
    y_label: str = None,
) -> tuple[VGroup, list, dict]:
    """Create a grouped bar chart using the GroupedBarChart class."""

    # 1. Transform data
    if not normalized_data or not normalized_data[0]:
        return VGroup(), [], {}

    series_names = dataset_names
    group_names = list(normalized_data[0].keys())

    grouped_data = {group: {} for group in group_names}
    for i, series_name in enumerate(series_names):
        if i < len(normalized_data):
            dataset = normalized_data[i]
            for group_name in group_names:
                grouped_data[group_name][series_name] = dataset.get(group_name, 0)

    # 2. Create series_colors
    series_colors = {name: color for name, color in zip(series_names, colors)}

    # 3. Create GroupedBarChart
    chart = GroupedBarChart(data=grouped_data, series_colors=series_colors)

    # 4. Customize look and feel using theme
    chart.axes.set_color(ThemeUtils.get_color("text_secondary"))
    for submob in chart.axes.submobjects:
        submob.set_color(ThemeUtils.get_color("text_secondary"))

    # Customize labels
    if x_label:
        x_axis_label = ThemeUtils.create_themed_text(x_label, "body", "text_primary")
        x_axis_label.next_to(chart.axes.x_axis, RIGHT, buff=ThemeUtils.get_spacing("sm"))
        chart.x_axis_label.become(x_axis_label)
    else:
        chart.remove(chart.x_axis_label)

    if y_label:
        y_axis_label = ThemeUtils.create_themed_text(y_label, "body", "text_primary")
        y_axis_label.rotate(90 * DEGREES)
        y_axis_label.next_to(chart.axes.y_axis, LEFT, buff=ThemeUtils.get_spacing("sm"))
        chart.y_axis_label.become(y_axis_label)
    else:
        chart.remove(chart.y_axis_label)

    # Customize dataset labels (groups on x-axis)
    dataset_labels = chart.get_dataset_labels(font_size=ThemeUtils.get_font_size("body"))
    dataset_labels.set_color(ThemeUtils.get_color("text_primary"))
    chart.add(dataset_labels)

    return chart, [chart], grouped_data


def _create_line_chart(
    normalized_data: list[dict],
    colors: list[str],
    x_label: str = None,
    y_label: str = None,
) -> VGroup:
    """Create a line chart with multiple datasets.

    Args:
        normalized_data: List of data dictionaries
        colors: List of colors for different datasets
        x_label: Label for the x-axis
        y_label: Label for the y-axis
    Returns:
        VGroup containing the complete line chart
    """
    # Calculate all values to find max for y-axis
    labels = list(normalized_data[0].keys())
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))

    # Create X values array (same for all datasets)
    x_vals = list(range(len(labels)))

    # Set coordinate ranges
    max_y = max(all_values) * 1.2
    min_y = 0
    y_step = max(1, int(max_y / 5))  # Limit to at most 5 major ticks

    x_range = [0, len(x_vals) - 1, 1]
    y_range = [min_y, max_y, y_step]

    # Create axes
    axes = Axes(
        x_range=x_range,
        y_range=y_range,
        axis_config={
            "color": ThemeUtils.get_color("text_primary"),
            "stroke_width": ThemeUtils.get_component_style("chart", "axis_stroke_width", 3.0),
        },
    )

    # Add y-axis tick labels
    for y_val in range(min_y, int(max_y) + 1, y_step):
        label_pos = axes.c2p(0, y_val)
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("body")
        text_color = ThemeUtils.get_color("text_primary")
        yl = Text(str(y_val), font_size=font_size, color=text_color, font=font)
        yl.next_to(label_pos, LEFT, buff=ThemeUtils.get_spacing("sm"))
        axes.add(yl)

    # Define different dot styles
    dot_styles = [
        {"fill_color": colors[0], "stroke_width": 1},
        {"fill_color": colors[1 % len(colors)], "stroke_width": 1},
        {"fill_color": colors[2 % len(colors)], "stroke_width": 1},
        {"fill_color": colors[3 % len(colors)], "stroke_width": 1},
    ]

    # Draw line graphs for each dataset
    line_graphs = []
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())

        # Draw line graph
        line_graph = axes.plot_line_graph(
            x_values=x_vals,
            y_values=dataset_values,
            line_color=colors[i % len(colors)],
            vertex_dot_radius=0.12,
            vertex_dot_style=dot_styles[i % len(dot_styles)],
            stroke_width=ThemeUtils.get_component_style("chart", "line_width", 4.0),
        )

        line_graphs.append(line_graph)

    # Add X-axis labels
    for i, label in enumerate(labels):
        label_pos = axes.c2p(i, 0)
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("body")
        text_color = ThemeUtils.get_color("text_primary")
        label_obj = Text(label, font_size=font_size, font=font, color=text_color)
        label_obj.next_to(label_pos, DOWN, buff=ThemeUtils.get_spacing("sm"))
        axes.add(label_obj)

    # Add axis unit labels if provided
    if x_label is not None:
        x_axis_label = ThemeUtils.create_themed_text(x_label, "body", "text_primary")
        x_axis_label.next_to(axes, DOWN, buff=ThemeUtils.get_spacing("lg"))
        axes.add(x_axis_label)

    if y_label is not None:
        y_axis_label = ThemeUtils.create_themed_text(y_label, "body", "text_primary")
        y_axis_label.rotate(90 * DEGREES)
        y_axis_label.next_to(axes, LEFT, buff=ThemeUtils.get_spacing("sm"))
        axes.add(y_axis_label)

    # Combine chart, including axes and all line graphs
    chart = VGroup(axes, *line_graphs)
    return chart


def _create_radar_chart(
    normalized_data: list[dict],
    colors: list[str],
) -> VGroup:
    """Create a radar chart with multiple datasets.

    Args:
        normalized_data: List of data dictionaries
        colors: List of colors for different datasets

    Returns:
        VGroup containing the complete radar chart
    """
    # Calculate max value for normalization
    labels = list(normalized_data[0].keys())
    all_values = []
    for dataset in normalized_data:
        all_values.extend(list(dataset.values()))
    logger.info(f"all_values: {all_values}")
    max_value = max(all_values)

    # Set radar chart parameters
    n = len(labels)
    radius = 2.5  # Initial radius, will be scaled later if needed
    center = ORIGIN

    # Create angles for the radar chart axes
    angles = [i * 2 * np.pi / n for i in range(n)]
    points = [center + radius * np.array([np.cos(angle), np.sin(angle), 0]) for angle in angles]

    # Create background polygon
    bg_polygon = Polygon(
        *points,
        color=ThemeUtils.get_color("text_primary"),
        stroke_width=ThemeUtils.get_component_style("chart", "axis_stroke_width", 1.0),
    )

    # Add concentric circle ticks
    circles = []
    for i in range(1, 5):  # Add 4 concentric circles
        ratio = i / 4
        circle_points = [
            center + radius * ratio * np.array([np.cos(angle), np.sin(angle), 0])
            for angle in np.linspace(0, 2 * np.pi, 50)
        ]
        circle = Polygon(
            *circle_points,
            color=ThemeUtils.get_color("text_secondary"),
            stroke_width=0.5,
            stroke_opacity=0.5,
            fill_opacity=0,
        )
        circles.append(circle)

    # Create axis lines
    axes = []
    for point in points:
        axes.append(
            Line(
                center,
                point,
                color=ThemeUtils.get_color("text_primary"),
                stroke_width=ThemeUtils.get_component_style("chart", "axis_stroke_width", 1.0),
            )
        )

    # Create polygons for each dataset
    data_polygons = []
    data_dots = []

    # Add axis labels
    label_objs = []
    label_backgrounds = []

    for i, label in enumerate(labels):
        # Calculate label position (outside the axis endpoint)
        angle = angles[i]
        # Use angle to correctly place label, and increase distance to avoid overlap
        direction = np.array([np.cos(angle), np.sin(angle), 0])
        # Dynamically adjust distance based on angle and label length
        # Especially increase distance for bottom labels
        is_bottom_label = np.pi * 0.4 < angle < np.pi * 1.6  # Identify labels in bottom area
        distance_factor = 0.8 if is_bottom_label else (0.7 if len(label) > 3 else 0.6)
        label_pos = center + (radius + distance_factor) * direction
        # Create label with Text, supporting Chinese
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("small")
        text_color = ThemeUtils.get_color("text_primary")
        label_obj = Text(label, font_size=font_size, font=font, color=text_color)
        label_obj.move_to(label_pos)
        # Add semi-transparent background box for better readability
        padding = 0.1
        background = Rectangle(
            height=label_obj.height + padding * 2,
            width=label_obj.width + padding * 2,
            fill_color=ThemeUtils.get_color("background"),
            fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.6),
            stroke_opacity=0,
        )
        background.move_to(label_obj.get_center())
        label_backgrounds.append(background)
        label_objs.append(label_obj)

    # Create value labels and backgrounds
    value_labels = []
    value_backgrounds = []

    # Create data polygons for each dataset
    for i, dataset in enumerate(normalized_data):
        dataset_values = list(dataset.values())

        # Scale values relative to max
        scaled_values = [val / max_value for val in dataset_values]

        # Calculate points for this dataset
        data_points = [
            center + radius * scaled_values[j] * np.array([np.cos(angles[j]), np.sin(angles[j]), 0]) for j in range(n)
        ]

        # Create polygon
        polygon = Polygon(
            *data_points,
            color=colors[i % len(colors)],
            fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.3),
            stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 2.0),
        )
        data_polygons.append(polygon)

        # Add data points
        dots = []
        for point in data_points:
            dot = Dot(point, color=colors[i % len(colors)], radius=0.1)
            dots.append(dot)
        data_dots.extend(dots)

        # Add value labels
        for j, val in enumerate(dataset_values):
            # Create value label
            font = ThemeUtils.get_font("primary")
            font_size = ThemeUtils.get_font_size("small")
            value_label = Text(str(val), font_size=font_size, color=colors[i % len(colors)], font=font)

            # Calculate label position
            angle = angles[j]
            scaled_value = scaled_values[j]
            direction = np.array([np.cos(angle), np.sin(angle), 0])
            data_point = center + radius * scaled_value * direction

            # Intelligently adjust label offset to avoid overlaps
            if scaled_value > 0.8:  # Near outer circle
                # Offset inward to avoid axis label overlap
                offset_direction = -0.3 * direction + 0.2 * np.array([-direction[1], direction[0], 0])
            elif scaled_value > 0.5:  # Medium distance
                # Lateral offset
                offset_direction = 0.1 * direction + 0.25 * np.array([-direction[1], direction[0], 0])
            else:  # Near center
                # Radial offset
                offset_direction = 0.3 * direction

            value_label.move_to(data_point + offset_direction)

            # Add semi-transparent background
            padding = 0.05
            value_bg = Rectangle(
                height=value_label.height + padding * 2,
                width=value_label.width + padding * 2,
                fill_color=ThemeUtils.get_color("background"),
                fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.6),
                stroke_opacity=0,
            )
            value_bg.move_to(value_label.get_center())
            value_backgrounds.append(value_bg)
            value_labels.append(value_label)

    # Combine all elements in visual order
    radar_elements = VGroup(
        bg_polygon,  # Background polygon at bottom
        *circles,  # Concentric circle ticks
        *axes,  # Axis lines
        *data_polygons,  # Data polygons
        *data_dots,  # Data points
    )

    # Add label backgrounds and labels
    if label_backgrounds:
        radar_elements.add(*label_backgrounds)
    if label_objs:
        radar_elements.add(*label_objs)

    # Add value label backgrounds and value labels
    if value_backgrounds:
        radar_elements.add(*value_backgrounds)
    if value_labels:
        radar_elements.add(*value_labels)

    return radar_elements


def _create_legend(
    dataset_names: list[str],
    colors: list[str],
) -> VGroup:
    """Create a legend for multiple datasets.

    Args:
        dataset_names: List of dataset names
        colors: List of colors for different datasets

    Returns:
        VGroup containing the legend
    """
    legend_items = []

    for i, name in enumerate(dataset_names):
        # Create color rectangle
        color_rect = Rectangle(
            height=0.2,
            width=0.4,
            fill_opacity=1,
            fill_color=colors[i],
            stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 1.0),
        )
        # Create text label
        font = ThemeUtils.get_font("primary")
        font_size = ThemeUtils.get_font_size("small")
        text_color = ThemeUtils.get_color("text_primary")
        label = Text(name, font_size=font_size, font=font, color=text_color)
        # Combine rectangle and label
        label.next_to(color_rect, RIGHT, buff=0.1)
        legend_item = VGroup(color_rect, label)
        legend_items.append(legend_item)

    # Arrange legend items
    legend = VGroup()
    for i, item in enumerate(legend_items):
        if i == 0:
            legend.add(item)
        else:
            # Place new item to the right of previous items
            item.next_to(legend, RIGHT, buff=0.3)
            legend.add(item)

    return legend


def _animate_bars_dynamic(scene: "FeynmanScene", chart_objects: list, final_data: dict, colors: list[str]):
    """Animate bars with dynamic value changes using GroupedBarChart.
    The colors argument is kept for signature compatibility but is not used.
    """
    if not chart_objects:
        return
    chart_obj = chart_objects[0]
    # The change_bar_values method returns an animation
    animation = chart_obj.change_bar_values(final_data)
    scene.play(animation, run_time=2.0, rate_func=smooth)
    bar_labels = chart_obj.get_bar_labels(font_size=20)
    scene.play(Write(bar_labels))
    return bar_labels


def animate_chart(
    scene: "FeynmanScene",
    chart_type: str,  # 'bar', 'line', or 'radar'
    data: dict | list[dict],
    title: str,
    animation_style: str = "dynamic",  # 'fadeIn', 'grow', 'draw', 'update', or 'dynamic'
    narration: Optional[str] = None,
    id: Optional[str] = None,
    dataset_names: Optional[list[str]] = None,
    x_label: Optional[str] = None,
    y_label: Optional[str] = None,
):
    logger.info(f"Animating chart '{id}' of type '{chart_type}' in region 'full_screen'.")

    # Generate a unique ID if not provided
    unique_id = id or f"chart_{abs(hash(str(data))) % 10000}"

    # Normalize data structure
    normalized_data, default_dataset_names = _normalize_data(data)

    # Use provided dataset names or defaults
    actual_dataset_names = dataset_names or default_dataset_names

    # Generate colors for datasets
    colors = _get_colors(len(normalized_data))

    # Create title object
    initial_title, title_text = create_title(title, scene=scene)

    # Create chart based on type and animation style
    chart_objects = None
    animation_data = None
    if chart_type == "bar":
        chart, chart_objects, animation_data = _create_enhanced_bar_chart(
            normalized_data, actual_dataset_names, colors, x_label, y_label
        )
    elif chart_type == "line":
        chart = _create_line_chart(normalized_data, colors, x_label, y_label)
    elif chart_type == "radar":
        chart = _create_radar_chart(normalized_data, colors)
    else:
        logger.error(f"Unsupported chart type: {chart_type}")
        return

    # Create legend
    legend = _create_legend(actual_dataset_names, colors)

    # Create content group with legend and chart
    content_group = VGroup(legend, chart)
    content_group.arrange(DOWN, buff=0.3)

    # Ensure chart fits the screen
    target_rect = scene.full_screen_rect
    if target_rect:
        # Calculate available space
        available_width = target_rect.width * 0.8
        available_height = (target_rect.height - title_text.height) * 0.8

        # Scale if necessary
        if content_group.width > 0 and content_group.height > 0:
            scale_factor = min(available_width / content_group.width, available_height / content_group.height)
            if scale_factor < 1.0:
                content_group.scale(scale_factor)

            # Center in target region
            content_group.move_to(target_rect.get_center()).next_to(title_text, DOWN, buff=0.3)

    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # Animate the chart with or without narration
    with scene.voiceover(text=narration) as tracker:
        narration_duration = tracker.duration if hasattr(tracker, "duration") else 0

        fade_duration = ThemeUtils.get_animation_duration("fade")
        default_duration = ThemeUtils.get_animation_duration("default")

        scene.add(title_text)
        scene.play(FadeIn(legend), run_time=fade_duration * 0.5)

        if chart_type == "bar" and chart_objects:
            # For GroupedBarChart, we add the chart with 0-height bars first,
            # which shows the axes and labels. Then we animate the bars growing.
            scene.play(Create(chart))
            bar_labels = _animate_bars_dynamic(scene, chart_objects, animation_data, colors)
            content_group.add(bar_labels)
        else:
            # Animate other chart types based on style
            if animation_style == "grow":
                scene.play(FadeIn(chart), run_time=fade_duration)
            elif animation_style == "fadeIn":
                scene.play(FadeIn(chart), run_time=fade_duration)
            elif animation_style == "draw":
                scene.play(Create(chart), run_time=default_duration)
            else:
                # Fallback to simple creation
                scene.play(Create(chart), run_time=default_duration)

        # Wait for narration to complete if needed
        effective_duration = max(3.0, narration_duration) - 4.5  # Subtract animation time
        if effective_duration > 0:
            scene.wait(effective_duration)

    # Create display group
    display_group = Group(initial_title, title_text, content_group)

    # Update current_mobject saving
    scene.current_mobj = display_group
    scene.save_scene_state(content_type="chart", mobject_id=unique_id)
    logger.info(f"Completed animating chart '{unique_id}' in region 'full_screen'")
