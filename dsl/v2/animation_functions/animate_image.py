"""
effect: |
    在Manim场景中显示图像，支持动态入场动画和叠加注释文本。图片会从右下角斜着入场，然后顺正并进行左右移动放大。

use_cases:
    - 展示需要详细解释的图片或截图
    - 展示图表、图解或可视化内容
    - 显示产品或界面截图并添加叠加注释说明
    - 突出显示图片中的关键要点

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  title:
    type: str
    desc: 当前内容的标题，会显示在内容上方
    required: true
  image_path:
    type: str
    desc: 要显示的图片的本地文件路径
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  narration:
    type: str
    desc: 在图片显示时播放的语音旁白文本
    required: true
  annotation:
    type: str或List[str]
    desc: 作为注释叠加显示在图片上的文本，使用发光效果和半透明背景
    default: None

dsl_examples:
    - type: animate_image
      params:
        title: 示例图片展示
        image_path: assets/manim_logo.png
        narration: 让我们看看这张图片。
        annotation: 这是一张示例图片，展示了重要的内容。
    - type: animate_image
      params:
        title: 系统架构图
        image_path: assets/manim_logo.png
        id: architecture_diagram
        annotation: [ "系统架构关键要点",
            "前端组件负责用户交互",
            "后端服务处理业务逻辑",
            "数据存储确保数据持久化"
        ]
        narration: 这张架构图展示了系统的主要组件和它们之间的关系。

notes:
    - 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
    - 图片动画分为四个步骤：1）从右下角斜着入场 2）移动到中心并放大 3）旋转顺正 4）左右移动并进一步放大
    - 如果提供annotation，它会以发光文字形式叠加显示在图片上，有半透明黑色背景
    - annotation支持字符串或列表格式，列表中每个元素会作为一行显示
    - 注释文字使用sequential动画逐行显示，提供更好的视觉效果
"""

import os
import random
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import create_text_with_emoji_unified
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.title_utils import create_title


# ---- 辅助函数 ----
def _create_image_mobject(image_path: str, **kwargs) -> ImageMobject | SVGMobject:
    """
    创建图像 Manim 对象。

    Args:
        image_path: 图像文件路径
        **kwargs: 其他 ImageMobject 参数

    Returns:
        ImageMobject | SVGMobject: Manim ImageMobject 对象或 SVGMobject 对象
    """
    if not os.path.exists(image_path):
        logger.error(f"图像文件未找到: {image_path}")
        raise FileNotFoundError(f"图像文件未找到: {image_path}")

    return SVGMobject(image_path, **kwargs) if image_path.endswith(".svg") else ImageMobject(image_path, **kwargs)


def _calculate_pan_animation_params(obj: Mobject, target_rect: Rectangle) -> dict[str, Any]:
    """
    计算图像平移动画的参数。

    Args:
        obj: 要平移的对象
        target_rect: 目标区域的矩形

    Returns:
        Dict: 包含平移动画参数的字典
    """
    params = {}

    # 获取原始尺寸
    orig_width = obj.width
    orig_height = obj.height

    # 获取目标区域尺寸
    target_width = target_rect.width
    target_height = target_rect.height

    # 目标区域中心
    target_center = target_rect.get_center()

    # 检查宽高比
    image_aspect_ratio = orig_width / orig_height
    if image_aspect_ratio > 1.5:  # 宽图像 -> 向左平移
        target_height_scaled = target_height * 0.95  # 从0.9提高到0.95
        scale_factor = target_height_scaled / orig_height if orig_height > 0 else 1.0
        obj.scale(scale_factor)
        scaled_width = obj.width

        # Initial position: Center of image is at target_right_edge + scaled_width / 2
        # (So, left edge of image is at target_right_edge)
        initial_pos_x = target_center[0] + (scaled_width / 4)
        initial_pos_y = target_center[1]
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_width / 2  # Pan half width
        pan_direction = LEFT
    elif image_aspect_ratio < 0.6:  # 长图像 -> 向上平移
        target_width_scaled = target_width * 0.95  # 从0.9提高到0.95
        scale_factor = target_width_scaled / orig_width if orig_width > 0 else 1.0
        obj.scale(scale_factor)
        scaled_height = obj.height

        # Initial position: Center of image is at target_bottom_edge - scaled_height / 2
        # (So, top edge of image is at target_bottom_edge)
        initial_pos_x = target_center[0]
        initial_pos_y = target_center[1] - (scaled_height / 4)
        initial_pos = np.array([initial_pos_x, initial_pos_y, target_center[2]])

        shift_dist = scaled_height / 2  # Pan half height
        pan_direction = UP
    else:
        # 不需要平移
        initial_pos = target_center
        shift_dist = 0
        pan_direction = None

    params["initial_pos"] = initial_pos
    params["shift_distance"] = shift_dist
    params["pan_direction"] = pan_direction
    params["target_rect"] = target_rect

    return params


def _animate_zoom_and_pan(
    scene: "FeynmanScene",
    obj: Mobject,
    narration: Optional[str],
    pan_params: dict[str, Any],
    annotation: Optional[str | list[str]] = None,
) -> Optional[Mobject]:
    """
    缩放和平移动画策略。

    Args:
        scene: Manim 场景
        obj: 要动画的对象
        narration: 旁白文本
        pan_params: 平移动画参数
        annotation: 图片注释文本（可选，支持字符串或列表）

    Returns:
        Optional[Mobject]: 注释文本对象
    """
    text_obj = None

    # 处理可能的None值
    if narration is None:
        narration = ""

    with scene.voiceover(text=narration) as tracker:
        # 获取目标区域和设置初始参数
        target_rect = pan_params["target_rect"]
        target_center = target_rect.get_center()

        # 计算初始缩放比例 - 提高缩放系数
        width_scale_factor = ThemeUtils.get_component_style("image", "initial_scale_width", 0.9)  # 从0.8提高到0.9
        height_scale_factor = ThemeUtils.get_component_style("image", "initial_scale_height", 0.9)  # 从0.8提高到0.9
        initial_scale_factor = min(
            float(target_rect.width) * width_scale_factor / float(obj.width),
            float(target_rect.height) * height_scale_factor / float(obj.height),
        )

        # 第一步：设置图片初始状态 - 随机轨迹斜着进入
        # 随机选择进入方向
        entry_directions = [
            np.array([float(target_rect.width) * 1.2, -float(target_rect.height) * 1.2, 0]),  # 右下角
            np.array([float(target_rect.width) * 1.2, float(target_rect.height) * 1.2, 0]),  # 右上角
            np.array([-float(target_rect.width) * 1.2, -float(target_rect.height) * 1.2, 0]),  # 左下角
            np.array([-float(target_rect.width) * 1.2, float(target_rect.height) * 1.2, 0]),  # 左上角
            np.array([float(target_rect.width) * 1.5, 0, 0]),  # 右侧
            np.array([-float(target_rect.width) * 1.5, 0, 0]),  # 左侧
            np.array([0, float(target_rect.height) * 1.5, 0]),  # 上方
            np.array([0, -float(target_rect.height) * 1.5, 0]),  # 下方
        ]

        # 随机选择进入位置
        entry_offset = random.choice(entry_directions)
        initial_pos = target_center + entry_offset

        # 随机生成旋转角度，增加丰富性
        rotation_angle = random.uniform(-40, 40) * DEGREES  # 简化为Z轴旋转：-40到40度

        # 设置初始状态：小尺寸，随机角度，在屏幕外
        obj.scale(initial_scale_factor).move_to(initial_pos).rotate(rotation_angle)

        # 第二步：随机轨迹斜着入场 - 移动到中心
        # 随机选择轨迹类型：直线、弧形或S型
        trajectory_type = random.choice(["direct", "arc", "s_curve"])

        if trajectory_type == "direct":
            # 直线轨迹：直接移动到中心
            scene.play(
                obj.animate.move_to(target_center),
                FadeIn(obj),
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.5),
            )
        elif trajectory_type == "arc":
            # 弧形轨迹：通过中间点移动
            intermediate_pos = target_center + np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.4, float(target_rect.width) * 0.4),  # 中间点随机偏移
                    random.uniform(-float(target_rect.height) * 0.3, float(target_rect.height) * 0.3),
                    0,
                ]
            )

            # 分两段移动：先到中间点，再到中心，创造弧形轨迹
            scene.play(
                obj.animate.move_to(intermediate_pos),  # 移除随机缩放
                FadeIn(obj),
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.3),
            )

            scene.play(
                obj.animate.move_to(target_center),  # 移除调整缩放
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.3),
            )
        else:  # s_curve
            # S型轨迹：通过两个中间点移动
            mid1_offset = np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.5, float(target_rect.width) * 0.5),
                    random.uniform(-float(target_rect.height) * 0.4, float(target_rect.height) * 0.4),
                    0,
                ]
            )
            mid2_offset = np.array(
                [
                    random.uniform(-float(target_rect.width) * 0.3, float(target_rect.width) * 0.3),
                    random.uniform(-float(target_rect.height) * 0.2, float(target_rect.height) * 0.2),
                    0,
                ]
            )

            intermediate_pos1 = target_center + mid1_offset
            intermediate_pos2 = target_center + mid2_offset

            # 三段移动：创造S形轨迹
            scene.play(
                obj.animate.move_to(intermediate_pos1).rotate(random.uniform(-15, 15) * DEGREES),  # 添加轻微旋转
                FadeIn(obj),
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.3),
            )

            scene.play(
                obj.animate.move_to(intermediate_pos2),  # 移除随机缩放
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.3),
            )

            scene.play(
                obj.animate.move_to(target_center),  # 移除调整缩放
                run_time=ThemeUtils.get_animation_duration("image_fade_in", 0.3),
            )

        # 第三步：顺正 - 将旋转恢复到0度，消除倾斜变为正面
        scene.play(
            obj.animate.rotate(-rotation_angle),  # 逆向旋转恢复正面
            run_time=ThemeUtils.get_animation_duration("image_rotation", 0.5),
        )

        # 第四步：智能定位和平移 - 进一步增大图片尺寸
        enlarge_factor = (
            max(target_rect.width / obj.width, target_rect.height / obj.height) * 0.98
        )  # 增加最终放大系数到0.98
        if pan_params["shift_distance"] > 0.01:
            # 先移动到初始位置并放大
            scene.play(
                obj.animate.move_to(pan_params["initial_pos"]).scale(enlarge_factor),
                run_time=ThemeUtils.get_animation_duration("image_position", 1),
            )
            # 然后进行平移
            max_pan_duration = ThemeUtils.get_component_style("image", "max_pan_duration", 4.0)
            scene.play(
                obj.animate.shift(pan_params["pan_direction"] * pan_params["shift_distance"]),
                run_time=min(max_pan_duration, tracker.duration * 2 / 3),
                rate_func=linear,
            )
        else:
            # 如果不需要平移，直接放大
            scene.play(obj.animate.scale(enlarge_factor), run_time=ThemeUtils.get_animation_duration("image_scale", 1))

        scene.play(obj.animate.move_to(target_rect.get_center()).scale(1 / enlarge_factor), run_time=0.5)
        scene.wait()
        # 如果有注释，在图片上叠加显示注释
        if annotation:
            text_obj = _create_sequential_text_lines_group(annotation)
            text_obj.set_z_index(1)  # 确保文字在图片上方
            text_obj.move_to(obj.get_center())  # 将文字组定位到图片中心

            # 根据图片大小缩放文字
            scale_factor_text = (
                min(float(obj.height) / float(text_obj.height), float(obj.width) / float(text_obj.width), 1.0) * 0.8
            )
            text_obj.scale(scale_factor_text)

            # 使用sequential动画方式显示文字
            # overlay_animation_delay = ThemeUtils.get_component_style("annotation", "overlay_animation_delay", 1.0)
            scene.play(
                LaggedStart(
                    *[FadeIn(line, shift=RIGHT * 0.2) for line in text_obj],
                    lag_ratio=0.3,
                    # run_time=max(1.0, len(text_obj) * overlay_animation_delay),
                ),
                run_time=ThemeUtils.get_animation_duration("image_reposition", 1),
            )
            wait_time = ThemeUtils.get_component_style("image", "annotation_wait_time", 2)
            scene.wait(wait_time)

    return text_obj


def _animate_annotation_only(
    scene: "FeynmanScene", annotation: str | list[str], target_rect: Rectangle, narration: Optional[str] = None
) -> Optional[Mobject]:
    """
    仅注释显示动画策略。

    Args:
        scene: Manim 场景
        annotation: 注释文本，支持字符串或列表格式
        target_rect: 目标区域的矩形
        narration: 旁白文本（可选）

    Returns:
        创建的注释文本对象
    """
    # 使用sequential text lines group创建文本
    text_obj = _create_sequential_text_lines_group(annotation)
    text_obj.move_to(target_rect.get_center())

    # 缩放以适应目标区域
    width_factor = 0.8
    height_factor = 0.8
    if text_obj.width > float(target_rect.width) * width_factor:
        text_obj.scale_to_fit_width(float(target_rect.width) * width_factor)
    if text_obj.height > float(target_rect.height) * height_factor:
        text_obj.scale_to_fit_height(float(target_rect.height) * height_factor)

    with scene.voiceover(text=narration) as tracker:  # noqa: F841
        # overlay_animation_delay = ThemeUtils.get_component_style("annotation", "overlay_animation_delay", 1.0)
        # 使用与animate_video类似的sequential动画方式
        scene.play(
            LaggedStart(
                *[FadeIn(line, shift=RIGHT * 0.2) for line in text_obj],
                lag_ratio=0.3,
                # run_time=max(1.0, len(text_obj) * overlay_animation_delay),
            )
        )
    return text_obj


def _create_sequential_text_lines_group(
    annotation: str | list[str],
    text_font_size: int = 48,
    text_color: ManimColor = YELLOW_B,
) -> Group:
    """
    创建文本行组，每行都有背景矩形，用于在图片上顺序显示注释。

    Args:
        annotation: 注释文本，可以是字符串或字符串列表
        text_font_size: 文字大小
        text_color: 文字颜色

    Returns:
        Group: 包含所有文本行的群组
    """
    # 处理不同格式的输入
    if isinstance(annotation, list):
        lines = [str(item).strip() for item in annotation if item and str(item).strip()]
    else:
        lines = str(annotation).strip().split("\n")
    line_mobjects_with_background = []
    max_text_width = 0

    # First pass: create text and rects, find max_width
    created_text_rect_pairs: list[tuple[Mobject, SurroundingRectangle]] = []  # 修复类型注解

    for i, line_content in enumerate(lines):
        text_line = create_text_with_emoji_unified(
            line_content,
            font_size=text_font_size,
            font_color=text_color,
        )
        # Transparent background for text, actual background is the rectangle
        rect = SurroundingRectangle(
            text_line,
            buff=0.4,
            fill_opacity=0.75,
            fill_color=BLACK,
            stroke_width=0,
            corner_radius=(text_line.height + 0.4) * 0.3,
        )
        max_text_width = max(max_text_width, float(text_line.width))
        created_text_rect_pairs.append((text_line, rect))

    # Second pass: adjust rect widths and group
    for i, (text_line, rect) in enumerate(created_text_rect_pairs):
        # Stretch rectangle to max_text_width (plus buff)
        rect.stretch_to_fit_width(max_text_width + 2 * rect.buff)  # Use rect.buff (which is 0.2)
        line_group = Group(rect, text_line)  # Rectangle behind text
        line_mobjects_with_background.append(line_group)

    text_lines_group = Group(*line_mobjects_with_background)
    text_lines_group.arrange(DOWN, buff=MED_LARGE_BUFF, aligned_edge=LEFT)  # Increased buff
    return text_lines_group


# ---- 主函数 ----
def animate_image(
    scene: "FeynmanScene",
    title: str,
    image_path: str,
    id: Optional[str] = None,
    narration: Optional[str] = None,
    annotation: Optional[str | list[str]] = None,
    cwd: str = None,
) -> None:
    # 1. 初始化和基本设置
    if not getattr(scene, "scene_method_name", None):
        scene.scene_method_name = "animate_image"
    # 创建唯一的引用 ID
    reference_id = id if id else f"image_{abs(hash(image_path)) % 10000}"

    logger.info(f"显示图片内容: region='full_screen', id='{reference_id}'")

    # Create title object
    initial_title, title_text = create_title(title, scene=scene)

    # 2. 获取目标区域并清除
    target_rect = scene.full_screen_rect
    # Handle clear_current_mobject for transition support
    scene.clear_current_mobj(new_mobj=initial_title if scene.transition_enabled else None)

    # annotation 参数现在由 _create_sequential_text_lines_group 函数直接处理

    # 3. 检查图像是否存在
    image_exists = os.path.exists(image_path)
    if not image_exists and cwd is not None:
        # 增加 image_path 的父文件夹前缀
        image_path = os.path.join(cwd, image_path)
        logger.info(f"updated image_path: {image_path}")
    image_exists = os.path.exists(image_path)

    if not image_exists:
        logger.warning(f"图像文件未找到: {image_path}")
        if not annotation:
            logger.warning("没有提供注释文本，跳过显示。")
            return

        # 处理仅有注释的情况
        logger.info("图像不存在但有注释文本，将全屏显示注释。")
        try:
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)

            # Always animate the annotation content with original effects
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)

            # Create display group with title and annotation
            display_group = Group(initial_title, title_text, text_obj)

            scene.current_mobj = display_group
        except Exception as e:
            logger.error(f"处理注释时出错: {e}")
        return

    # 4. 创建图像对象
    try:
        obj = _create_image_mobject(image_path)
    except Exception as e:
        logger.error(f"创建图像对象时出错: {e}")
        if not annotation:
            return

        # 如果图像创建失败但有注释，回退到仅显示注释
        logger.info("图像创建失败但有注释文本，将全屏显示注释。")
        try:
            # 直接添加标题，动画已在 create_title 中处理
            scene.add(title_text)

            # Always animate the annotation content with original effects
            text_obj = _animate_annotation_only(scene, annotation, target_rect, narration)

            # Create display group with title and annotation
            display_group = Group(initial_title, title_text, text_obj)

            scene.current_mobj = display_group
        except Exception as e2:
            logger.error(f"处理注释时出错: {e2}")
        return

    # 5. 计算动画参数
    pan_params = _calculate_pan_animation_params(obj, target_rect)

    # 6. 播放动画
    # 直接添加标题，动画已在 create_title 中处理
    scene.add(title_text)

    # Always animate the image content with original effects
    text_obj = _animate_zoom_and_pan(scene, obj, narration, pan_params, annotation)

    # Create display group with title and content
    # 构建内容群组，包含图片和注释
    content_elements: list[Mobject] = [obj]  # 修复类型注解
    if text_obj:
        content_elements.append(text_obj)

    content_group = Group(*content_elements) if len(content_elements) > 1 else content_elements[0]

    # 创建显示组：initial_title用于转场，title_text用于显示，content为实际内容
    # 转场系统会使用submobjects[0]（initial_title）作为转场对象
    display_group = Group(initial_title, title_text, content_group)

    # 7. Update current_mobject saving - ensure title is first submobject
    scene.current_mobj = display_group
    scene.save_scene_state(content_type="image", mobject_id=reference_id)
    logger.info(f"显示图片完成: image_path={image_path}, id='{reference_id}'")
