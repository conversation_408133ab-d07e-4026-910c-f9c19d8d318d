# BPE算法动画场景实现指南

## 场景配置
- 模板：ProfessionalScienceTemplate
- 主题：BPE实战演练  
- 布局：完整布局
- 时长：90秒

## 必需设置
```python
self.setup_background()
```

## 核心数据
```python
original_texts = ["你好棒你真棒", "你好酷你好棒"]
vocab_stages = [
    {"你": 4, "好": 4, "棒": 3, "真": 1, "酷": 1},
    {"你好": 4, "棒": 3, "真": 1, "酷": 1},
    {"你好棒": 3, "真": 1, "酷": 1}
]
```

## 区域内容定义
```python
title = self.create_title_region_content("BPE实战演练")
steps = ["初始化词表", "第一次合并", "第二次合并", "合并完成", "文本编码"]
left_aux = self.create_left_auxiliary_content("语料与操作", 
    ["原始语料", "切分汉字", "最高频查找", "构建新词", "文本替换"])
results = ["算法通过迭代合并构建词表", "发现最高频对并合并", "迭代合并继续", 
          "达到合并次数限制", "通过学到的子词词表编码"]
```

## 动画序列

### 阶段1：初始化 (15秒)
```python
# 显示标题和步骤
self.play(Write(title), Write(self.create_step_region_content(steps[0])))
self.play(FadeIn(left_aux, shift=LEFT))

# 显示原始文本
text_mobjects = VGroup(*[Text(t) for t in original_texts]).arrange(DOWN)
main_content = self.create_main_region_content(text_mobjects)
self.play(FadeIn(main_content))

# 高亮当前操作
self.play(left_aux[0].animate.set_color(YELLOW))

# 分解为字符块
char_blocks = self.create_char_blocks(original_texts)
self.play(Transform(main_content, char_blocks))

# 显示初始词表
vocab_display = MathTex(r"\text{词表：}\{你:4, 好:4, 棒:3, 真:1, 酷:1\}")
right_aux = self.create_right_auxiliary_content("数据与结果", [vocab_display])
self.play(FadeIn(right_aux, shift=RIGHT))
```

### 阶段2：第一次合并 (20秒)
```python
# 更新步骤
self.play(Transform(step_group, self.create_step_region_content(steps[1])))

# 高亮最高频对
self.play(self.highlight_char_pairs(char_blocks, "你", "好"))

# 显示合并动画
merged_blocks = self.merge_char_blocks(char_blocks, "你", "好", "你好")
self.play(Transform(main_content, merged_blocks))

# 更新词表
new_vocab = MathTex(r"\text{词表：}\{你好:4, 棒:3, 真:1, 酷:1\}")
self.play(Transform(right_aux, self.create_right_auxiliary_content("数据与结果", [new_vocab])))
```

### 阶段3：第二次合并 (20秒)
```python
# 重复合并流程：你好 + 棒 = 你好棒
self.play(Transform(step_group, self.create_step_region_content(steps[2])))
self.play(self.highlight_char_pairs(main_content, "你好", "棒"))
final_blocks = self.merge_char_blocks(main_content, "你好", "棒", "你好棒")
self.play(Transform(main_content, final_blocks))
```

### 阶段4：完成训练 (10秒)
```python
self.play(Transform(step_group, self.create_step_region_content(steps[3])))
final_vocab = MathTex(r"\text{最终词表：}\{你,好,棒,真,酷,你好,你好棒\}")
self.play(Transform(right_aux, self.create_right_auxiliary_content("数据与结果", [final_vocab])))
```

### 阶段5：文本编码 (25秒)
```python
# 显示新句子
new_sentence = Text("你好棒真棒")
self.play(FadeOut(main_content), FadeIn(new_sentence))

# 逐步匹配和编码
matches = [("你好棒", YELLOW), ("真", GREEN), ("棒", BLUE)]
encoded_result = []
for word, color in matches:
    rect = SurroundingRectangle(new_sentence[...], color=color)  # 需要实现字符索引
    self.play(Create(rect))
    encoded_result.append(word)
    self.wait(1)
    self.play(FadeOut(rect))

# 显示最终编码结果
result_text = Text(f"编码结果：{encoded_result}")
self.play(FadeIn(result_text))
```

## 必需实现的辅助方法
```python
def create_char_blocks(self, texts):
    # 将文本转换为可操作的字符方块
    blocks = VGroup()
    for text in texts:
        text_blocks = VGroup()
        for char in text:
            block = Rectangle(width=0.5, height=0.5).add(Text(char, font_size=24))
            text_blocks.add(block)
        text_blocks.arrange(RIGHT, buff=0.1)
        blocks.add(text_blocks)
    blocks.arrange(DOWN, buff=0.3)
    return blocks

def highlight_char_pairs(self, blocks, char1, char2):
    # 高亮显示字符对
    animations = []
    for block_group in blocks:
        for i in range(len(block_group) - 1):
            if (block_group[i][1].text == char1 and 
                block_group[i+1][1].text == char2):
                animations.extend([
                    block_group[i].animate.set_color(YELLOW),
                    block_group[i+1].animate.set_color(YELLOW)
                ])
    return AnimationGroup(*animations)

def merge_char_blocks(self, blocks, char1, char2, merged_char):
    # 执行字符块合并动画
    new_blocks = VGroup()
    for block_group in blocks:
        new_group = VGroup()
        i = 0
        while i < len(block_group):
            if (i < len(block_group) - 1 and 
                block_group[i][1].text == char1 and 
                block_group[i+1][1].text == char2):
                # 创建合并后的块
                merged_block = Rectangle(width=1.0, height=0.5).add(
                    Text(merged_char, font_size=24))
                new_group.add(merged_block)
                i += 2  # 跳过两个字符
            else:
                new_group.add(block_group[i].copy())
                i += 1
        new_group.arrange(RIGHT, buff=0.1)
        new_blocks.add(new_group)
    new_blocks.arrange(DOWN, buff=0.3)
    return new_blocks
```

## 关键动画技巧
- 使用Transform进行平滑过渡
- 用SurroundingRectangle高亮重要元素  
- FadeIn/FadeOut控制元素出现消失
- 颜色变化突出当前操作状态

## 验证标准
- 所有区域内容符合字数限制
- 动画流畅无卡顿
- 字符块合并逻辑正确
- 编码演示清晰易懂

## 常见问题解决
1. 字符索引问题：使用Text对象的submobjects属性
2. 布局溢出：调整font_size和buff参数
3. 动画不同步：使用AnimationGroup统一控制
4. 内存占用：及时清理不用的mobjects
